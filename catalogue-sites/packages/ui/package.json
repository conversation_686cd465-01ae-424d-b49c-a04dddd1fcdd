{"name": "@catalogue-sites/ui", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "next dev", "dev:all": "ts-node preview-all-sites.ts", "build": "next build", "start": "next start", "lint": "next lint", "lint:tf": "terraform fmt -recursive -check -diff", "format:tf": "terraform fmt -recursive", "typecheck": "tsc --noEmit", "lighthouse": "lhci autorun", "test": "jest"}, "dependencies": {"@apollo/client": "3.13.8", "@apollo/client-integration-nextjs": "0.12.2", "@atg-digital/ui-components": "40.9.1", "@atg-digital/ui-content-blocks": "32.1.3", "@atg-digital/ui-forms": "28.1.23", "@atg-digital/ui-icons": "5.5.0", "@atg-digital/ui-idp-forms": "8.22.2", "@atg-digital/ui-navigation": "23.5.2", "@atg-digital/ui-seatmap": "7.3.0", "@atg-digital/ui-tokens": "33.2.0", "@aws-sdk/client-dynamodb": "3.876.0", "@aws-sdk/client-sts": "3.876.0", "@aws-sdk/credential-providers": "3.876.0", "@aws-sdk/util-dynamodb": "3.876.0", "@cld-apis/types": "^0.1.6", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/server": "11.11.0", "@emotion/styled": "11.14.1", "@mui/material": "7.1.0", "@mui/material-nextjs": "^7.1.0", "@next/third-parties": "15.5.2", "@optimizely/react-sdk": "3.2.4", "@sentry/nextjs": "10.6.0", "@sindresorhus/slugify": "2.2.1", "apollo-link-timeout": "4.0.0", "cloudinary-build-url": "^0.2.4", "dayjs": "1.11.14", "embla-carousel": "8.6.0", "embla-carousel-react": "8.6.0", "graphql": "16.11.0", "i18next": "25.4.2", "js-cookie": "3.0.5", "jwt-decode": "4.0.0", "loglevel": "1.9.2", "newrelic": "13.2.1", "next": "15.5.2", "react": "19.1.1", "react-countdown": "2.3.6", "react-device-detect": "2.2.3", "react-dom": "19.1.1", "server-only": "0.0.1", "sharp": "0.34.3", "store": "2.0.12", "ts-node": "10.9.2", "typesense": "2.1.0", "use-debounce": "10.0.5", "uuid": "11.1.0"}, "devDependencies": {"@atg-digital/config": "3.2.0", "@atg-digital/eslint-config": "3.0.4", "@next/eslint-plugin-next": "15.1.8", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@tsconfig/next": "2.0.3", "@types/jest": "30.0.0", "@types/node": "24.3.0", "@types/react": "19.1.11", "@types/react-dom": "19.1.8", "@types/store": "2.0.5", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "6.7.2", "eslint": "8.49.0", "eslint-config-next": "15.3.1", "eslint-config-prettier": "9.0.0", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-import": "2.28.1", "eslint-plugin-jest": "27.4.0", "eslint-plugin-jest-formatting": "3.1.0", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "5.0.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-testing-library": "6.0.1", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "lint-staged": "16.0.0", "playwright": "1.55.0", "prettier": "3.0.3", "ts-jest": "29.2.5", "typescript": "5.9.2"}}