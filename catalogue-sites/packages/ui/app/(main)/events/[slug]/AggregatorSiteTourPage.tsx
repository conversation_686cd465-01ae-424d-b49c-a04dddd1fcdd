'use client'

import {
  Grid,
  Box,
  type BreadcrumbsProps,
  InfoBar,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  RichTextParser,
  SectionTitle,
  type MediaProps,
} from '@atg-digital/ui-components'
import { KeyboardArrowDownIcon } from '@atg-digital/ui-icons'
import { useTheme } from '@mui/material'
import { t } from 'i18next'
import React, { useEffect, useState, useMemo } from 'react'

import type { CastItem } from './types'
import { CastSection } from './atoms/CastSection'
import { Hero } from './atoms/Hero'
import { GridContainer } from './atoms/GridContainer'
import { getCloudinaryImage } from './cloudinary-utils'

import { BreadcrumbsWithSchema } from 'components/BreadcrumbsWithSchema'
import { type UpsellProps, Upsell } from 'components/Upsell'
import { emitGeneralEvent } from 'lib/dataLayer/general'

type AggregatorSiteTourPageProps = {
  eventName: string
  genres?: string[]
  websiteSlug: string
  breadcrumbs?: BreadcrumbsProps['breadcrumbs']
  resolvedInfoBar?: string[]
  faqs?: {
    body?: string
    title?: string
  }[]
  castList?: CastItem[] | null
  upsell?: UpsellProps
  soundtrack?: string
  // TODO: Improve the type
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  tourShows?: any[]
  hero: {
    heroImage: { [key: number]: string }
    videoUrl?: string
    description?: string
    imageGallery?: {
      src: string
      alt: string
      srcSets: {
        gallery: MediaProps['imageSrc']
        carousel: string[]
      }
    }[]
    cta?: {
      label: string
      href: string
    }
    lifecycle?: string
    lifecycleSubtitle?: string
    infoBarContent?: string
  }
  isSeason?: boolean
}

export const AggregatorSiteTourPage = ({
  eventName,
  websiteSlug,
  genres,
  tourShows,
  breadcrumbs,
  resolvedInfoBar,
  faqs = [],
  castList = [],
  soundtrack,
  hero,
  isSeason = false,
  upsell,
}: AggregatorSiteTourPageProps) => {
  const { tokens } = useTheme()
  const [pageHasMounted, setPageHasMounted] = useState(false)

  const hasCastAndCreativesSection = castList && castList.length > 0
  const hasSoundtrackSection = Boolean(soundtrack)
  const hasUpsell = Boolean(upsell)
  const hasFaqSection = faqs.length > 0
  const showSideBar = !isSeason && hasUpsell

  const templateAreas = showSideBar
    ? { xs: '"main" "sidebar"', lg: '"main sidebar"' }
    : '"main"'
  const templateColumnsLg = showSideBar ? '2fr 1fr' : 'auto'

  useEffect(() => {
    setPageHasMounted(true)
  }, [])

  // we do this to avoid re-rendering the RichTextParser unnecessarily
  const parsedFaqs = useMemo(
    () =>
      faqs.map((faq) => (
        <RichTextParser
          key={faq.title}
          richText={faq.body || ''}
          imageTransformFunction={getCloudinaryImage}
        />
      )),
    [faqs]
  )

  // this is usually going to be a spotify iframe
  // we try to avoid re-rendering as much as possible and only render this client side
  // as spotify will return 429 if too many requests are made
  const soundtrackBody = useMemo(
    () => (
      <RichTextParser
        richText={soundtrack || ''}
        imageTransformFunction={getCloudinaryImage}
      />
    ),
    [soundtrack]
  )

  return (
    <>
      {breadcrumbs && <BreadcrumbsWithSchema breadcrumbs={breadcrumbs} />}
      {resolvedInfoBar && <InfoBar>{resolvedInfoBar}</InfoBar>}

      <Hero
        heroImage={hero.heroImage}
        description={hero.description}
        videoUrl={hero.videoUrl}
        imageGallery={hero.imageGallery}
        title={eventName}
        lifecycle={hero.lifecycle}
        lifecycleSubtitle={hero.lifecycleSubtitle}
        cta={hero.cta}
        infoBarContent={hero.infoBarContent}
      />

      <GridContainer>
        <Grid size={{ xs: 11, md: 9, lg: 10, xl: 8 }}>
          <Stack
            width="100%"
            display="grid"
            gridTemplateColumns={{
              xs: 'auto',
              lg: templateColumnsLg,
            }}
            gridTemplateRows="auto"
            gridTemplateAreas={templateAreas}
            columnGap={5}
            rowGap={{ xs: 5, lg: 7 }}
            mt={{ xs: 4, lg: 7 }}
            pb={{ xs: 5, lg: 7 }}
          >
            <Stack gridArea="main" spacing={{ xs: 5, lg: 7 }}>
              <Stack>
                <p>Details</p>
                <p>Genres: {genres?.join(', ')}</p>
                {/* TODO: You cannot have an inventorySlug for a tour */}
                {/* <DetailsSection inventorySlug={inventorySlug} genres={genres} /> */}
              </Stack>

              <div>
                <h3>Tour Count: {tourShows?.length}</h3>
                {tourShows?.map((show) => (
                  <>
                    <p>{show.venueTheatreDistrict}</p>
                    <p>
                      {show.title} - {show.venueName}
                    </p>

                    <a href={`/${websiteSlug}/${show.venueSlug}`}>More info</a>

                    <hr />
                  </>
                ))}
              </div>

              {hasCastAndCreativesSection && (
                <Stack>
                  <CastSection cast={castList} />
                </Stack>
              )}

              {hasSoundtrackSection && (
                <Stack
                  rowGap={2}
                  onClick={() =>
                    emitGeneralEvent('onClickPlaySoundtrack', websiteSlug)
                  }
                >
                  <SectionTitle
                    title={t('aggregatorSiteShowPage.soundtrack.title')}
                  />
                  {pageHasMounted ? soundtrackBody : undefined}
                </Stack>
              )}

              {hasFaqSection && (
                <Stack rowGap={2}>
                  <SectionTitle
                    title={t('aggregatorSiteShowPage.faqs.title')}
                  />
                  <Stack>
                    {faqs.map((faq, index) => (
                      <Accordion
                        onClick={() =>
                          emitGeneralEvent('onClickFaqExpand', faq.title || '')
                        }
                        key={index}
                        slotProps={{ transition: { unmountOnExit: false } }}
                      >
                        <AccordionSummary
                          aria-controls={`accordion-details-content-${index}`}
                          id={`faqs-${index}`}
                          expandIcon={
                            <KeyboardArrowDownIcon
                              color={tokens.components_accordion_fg}
                            />
                          }
                        >
                          <Typography component="span" variant="labelSmall">
                            {faq.title}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>{parsedFaqs[index]}</AccordionDetails>
                      </Accordion>
                    ))}
                  </Stack>
                </Stack>
              )}
            </Stack>

            {showSideBar && (
              <Stack gridArea="sidebar" display={{ xs: 'none', lg: 'block' }}>
                {upsell && <Upsell {...upsell} />}
              </Stack>
            )}
          </Stack>
        </Grid>
      </GridContainer>
      {upsell && (
        <Box
          bgcolor={tokens.foundations_palette_deepPurple_50}
          display={{
            xs: 'block',
            lg: isSeason ? 'block' : 'none',
          }}
          py={{ xs: 5, sm: 7 }}
        >
          <GridContainer>
            <Grid size={{ xs: 11, md: 9, lg: 10, xl: 8 }}>
              <Box display={{ xs: 'block', sm: 'none' }}>
                <Upsell {...upsell} />
              </Box>
              <Box display={{ xs: 'none', sm: 'block' }}>
                <Upsell {...upsell} direction="horizontal" />
              </Box>
            </Grid>
          </GridContainer>
        </Box>
      )}
    </>
  )
}
