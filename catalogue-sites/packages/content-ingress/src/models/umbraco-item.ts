import {
  AttributeValue,
  Put<PERSON><PERSON><PERSON>ommand,
  Delete<PERSON>tem<PERSON>ommand,
  GetItemCommand,
  QueryCommand,
} from '@aws-sdk/client-dynamodb'
import { marshall, unmarshall } from '@aws-sdk/util-dynamodb'

import { getDynamoDBClient } from '../clients'
import { UmbracoLocale } from '../types'

const dynamodbClient = getDynamoDBClient()

export class UmbracoItem {
  private static readonly EVENTS_PATH_PREFIX = '/events/'
  private static readonly CONTENT_TYPES = {
    TOUR: 'tour',
    PRODUCTION: 'production',
  } as const

  private static readonly BRANDS = {
    ATG_US: 'atgus',
    ATG_UK: 'atgtk',
  } as const

  private static readonly ITEM_TYPES = {
    CONTENT: 'CONTENT',
    SETTINGS: 'SETTINGS',
    TOUR: 'TOUR',
    PRODUCTION: 'PRODUCTION',
  } as const

  type: 'CONTENT' | 'SETTINGS' | 'TOUR' | 'PRODUCTION'
  pageUrl: string
  sourceType: string
  sourceId: string
  locale: UmbracoLocale
  name: string
  settings?: Record<string, unknown>
  brandConfig?: Record<string, unknown>;
  [key: string]: unknown

  constructor(params: Partial<UmbracoItem>) {
    Object.keys(params).forEach((key) => {
      this[key] = params[key]
    })

    this.locale = params.locale || 'en-US'
    this.name = params.name || ''

    // Set type based on content type - tours get "TOUR" PK, others get "CONTENT"
    this.type = this.isTour(params.contentTypeAlias)
      ? 'TOUR'
      : params.type || 'CONTENT'

    // Resolve the page URL based on content type
    this.pageUrl = this.resolvePageUrl(params)

    // Set source information
    this.sourceType = 'umbraco'
    this.sourceId = params._id as string
  }

  private isTour(contentTypeAlias: unknown): boolean {
    return contentTypeAlias === UmbracoItem.CONTENT_TYPES.TOUR
  }

  private getSlugFromParams(
    params: Record<string, unknown>
  ): string | undefined {
    const websiteSlug = params.websiteSlug as string | undefined
    return websiteSlug
  }

  private resolvePageUrl(params: Partial<UmbracoItem>): string {
    if (this.isTour(params.contentTypeAlias)) {
      const slug = this.getSlugFromParams(params)
      if (!slug) {
        throw new Error(
          `Tour event missing required websiteSlug field: ${JSON.stringify({
            id: params._id,
            name: params.name,
          })}`
        )
      }
      return slug.replace(/^\/+|\/+$/g, '')
    }

    // Default: use provided pageUrl or throw error if missing
    if (!params.pageUrl) {
      throw new Error(
        `Content missing required pageUrl field: ${JSON.stringify({
          id: params._id,
          name: params.name,
          contentType: params.contentTypeAlias,
        })}`
      )
    }
    return params.pageUrl
  }

  get contentKeys() {
    return {
      PK: this.type,
      SK: this.pageUrl,
      GSI1PK: this.sourceType,
      GSI1SK: this.sourceId,
    }
  }

  get settingsKeys() {
    return {
      PK: 'SETTINGS',
      SK: '/',
    }
  }

  toContentItem(): Record<string, AttributeValue> {
    return marshall(
      {
        ...this.contentKeys,
        ...Object.keys(this).reduce(
          (acc, key) => {
            if (
              !(typeof this[key] !== 'function') ||
              !['type', 'settings'].includes(key)
            ) {
              acc[key] = this[key]
            }
            return acc
          },
          {} as Record<string, unknown>
        ),
      },
      {
        convertClassInstanceToMap: true,
        removeUndefinedValues: true,
      }
    )
  }

  toSettingsItem(): Record<string, AttributeValue> {
    return marshall(
      {
        ...this.settingsKeys,
        ...Object.keys(this.settings || {}).reduce(
          (acc, key) => {
            acc[key] = this.settings?.[key]
            return acc
          },
          {} as Record<string, unknown>
        ),
      },
      {
        convertClassInstanceToMap: true,
        removeUndefinedValues: true,
      }
    )
  }

  static fromItem(item: Record<string, AttributeValue>): UmbracoItem {
    const data = unmarshall(item)
    return new UmbracoItem({
      type: data.PK,
      pageUrl: data.SK,
      _id: data.GSI1SK,
      ...Object.getOwnPropertyNames(data).reduce(
        (acc, key) => {
          if (!['PK', 'SK', 'GSI1PK', 'GSI1SK'].includes(key)) {
            acc[key] = data[key]
          }
          return acc
        },
        {} as Record<string, unknown>
      ),
    })
  }

  static async getContent(pageUrl: string): Promise<UmbracoItem | undefined> {
    // First try to find as regular content
    const item = await dynamodbClient.send(
      new GetItemCommand({
        TableName: process.env.CONTENT_DYNAMODB_TABLE,
        Key: {
          PK: { S: 'CONTENT' },
          SK: { S: pageUrl },
        },
      })
    )

    if (!item.Item) {
      return undefined
    }
    return UmbracoItem.fromItem(item.Item)
  }

  static async getAllContentUnderPageUrl(
    pageUrl: string
  ): Promise<UmbracoItem[]> {
    const items = await dynamodbClient.send(
      new QueryCommand({
        TableName: process.env.CONTENT_DYNAMODB_TABLE,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
          ':pk': { S: 'CONTENT' },
          ':sk': { S: pageUrl },
        },
      })
    )
    if (!items.Items) {
      return []
    }
    return items.Items.map((item) => UmbracoItem.fromItem(item))
  }

  static async getById(id: string): Promise<UmbracoItem | undefined> {
    const items = await dynamodbClient.send(
      new QueryCommand({
        TableName: process.env.CONTENT_DYNAMODB_TABLE,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :sourceType AND GSI1SK = :sourceId',
        ExpressionAttributeValues: {
          ':sourceType': { S: 'umbraco' },
          ':sourceId': { S: id },
        },
      })
    )
    if (!items.Items || items.Items.length === 0) {
      return undefined
    } else if (items.Items.length > 1) {
      console.warn(`Found ${items.Items.length} items with sourceId ${id}`)
    }
    return UmbracoItem.fromItem(items.Items[0])
  }

  static async getSettings(): Promise<UmbracoItem> {
    const item = await dynamodbClient.send(
      new GetItemCommand({
        TableName: process.env.CONTENT_DYNAMODB_TABLE,
        Key: {
          PK: { S: 'SETTINGS' },
          SK: { S: '/' },
        },
      })
    )

    if (!item.Item) {
      throw new Error('No settings found')
    }

    return UmbracoItem.fromItem(item.Item)
  }

  async saveContent() {
    await dynamodbClient.send(
      new PutItemCommand({
        TableName: process.env.CONTENT_DYNAMODB_TABLE,
        Item: this.toContentItem(),
      })
    )
    return this
  }

  async saveSettings() {
    await dynamodbClient.send(
      new PutItemCommand({
        TableName: process.env.CONTENT_DYNAMODB_TABLE,
        Item: this.toSettingsItem(),
      })
    )
    return this
  }

  async delete() {
    const item = this.toContentItem()
    await dynamodbClient.send(
      new DeleteItemCommand({
        ReturnValues: 'ALL_OLD',
        Key: {
          PK: item.PK,
          SK: item.SK,
        },
        TableName: process.env.CONTENT_DYNAMODB_TABLE,
      })
    )

    return this
  }
}
