import {
  BrandSchema,
  ContentSchema,
  EventDetailsSchema,
  OnSaleDateRangeSchema,
  ProductionCtasSchema,
  ProductionMediaSchema,
  ProductionMetadataSchema,
  ProductionSchema,
  ProductionSettingsSchema,
  VenueSchema,
  WifiContentSchema,
} from '@atg-digital/cms-types'
import { TourSchema } from '@atg-digital/cms-types/src/tour.ts'

import { mapUmbracoLink, safeParseOrWarn } from '../validate'
import { notifyGenericWarningSlack } from '../notify-slack'

import {
  getBrandAndPathDomain,
  getIdAndLabel,
  getLink,
  mapAwsRegion,
  mapUmbracoImage,
  normalizeDate,
  parseUmbracoStringFields,
  umbracoAccessibilityMapper,
} from './common'
import { mapUmbracoBlocks } from './block'
import {
  buildAuditMeta,
  buildEditorialMeta,
  buildSocialMetaData,
} from './metadata'

import type { SocialMetaInput } from './metadata'
import type { Tour } from '@atg-digital/cms-types/src/tour.ts'
import type {
  Brand,
  Content,
  EventDetails,
  Inventory,
  Link,
  OnSaleRangeDate,
  Production,
  ProductionMedia,
  ProductionMetadata,
  ProductionSettings,
  Venue,
  WifiContent,
} from '@atg-digital/cms-types'
import type { ZodError } from 'zod'
import type { Content as UmbracoContent } from '@umbraco/headless-client'
import type { Logger } from '@atg-digital/server-logger-library'
import type { InventoryContent } from '../../interfaces/inventory'
import type { VenueConfig as UmbracoVenueConfig } from '../../interfaces/venue-config'
import type {
  Block,
  UmbracoImage,
  UmbracoLink,
  WithBrand,
} from '../../interfaces/generics'
import type { BrandContent } from '../../interfaces/brand'
import type { ProductionContent } from '../../interfaces/production'
import type { TourContent } from '../../interfaces/tour'

const extractPageSlug = (url: string): string => {
  return url.replace(/\/+$/, '').split('/').pop() || ''
}

/* ═════════════ Venue helpers ═════════════ */

const parseVenue = (
  payload: WithBrand<ProductionContent>,
  logger: Logger
): Venue =>
  safeParseOrWarn(
    VenueSchema,
    {
      id: payload.venueConfig!.contentfulVenueId,
      contentTypeAlias: 'venue',
      name: payload.venueConfig!.venueConfigName,
      slug: payload.venueConfig!.venueSlug.toLowerCase(),
      isATGVenue: true,
      upsellTypes: [],
      config: {
        configName: payload.venueConfig!.venueConfigName,
        inventoryId: payload.venueConfig!.inventoryID,
        venueConfigSlug: payload.venueConfig!.venueSlug.toLowerCase(),
        isThirdParty: false,
        hideMembershipPromotion: false,
      },
    },
    payload,
    logger
  )

/* ═════════════ Production-specific parsers ═════════════ */

const parseOnSaleDates = (
  payload: ProductionContent,
  tz: string | undefined,
  logger: Logger
): OnSaleRangeDate[] =>
  payload.onSaleDates.map((d) =>
    safeParseOrWarn(
      OnSaleDateRangeSchema,
      {
        contentTypeAlias: 'onSaleDate',
        isSoldOut: d.soldOut,
        promoCode: d.promoCode,
        onSaleDate: normalizeDate(d.onSaleDate, tz),
        onSaleType: d.onSaleType as 'General' | 'Member' | 'Promo/Access',
        description: d.description,
        hideDateAndTime: d.hideDateAndTime,
        offSaleDate: normalizeDate(d.offSaleDate, tz),
      },
      payload,
      logger
    )
  )

/**
 * Separates published and unpublished entities from the production content.
 * Unpublished entities are used for notification purposes.
 */
const getPublishedEntities = (
  productionContent: ProductionContent,
  logger: Logger
) => {
  const publishedEntities: ProductionMetadata['entities'] = []
  const unpublishedEntitiesRoles: string[] = []

  productionContent.entities?.forEach((entity) => {
    // Just an alias.
    const performanceEntity = entity.content.performanceEntity

    // Ignore unpublished entities and store them to be notified.
    if (performanceEntity === undefined) {
      unpublishedEntitiesRoles.push(
        // This is the only fiedl we can get, since unpublished entities won't return the actuall entity details.
        entity.content.performanceEntityRole || 'undefined role'
      )
      return
    }

    // Maps the published entity
    publishedEntities.push({
      contentTypeAlias: 'performanceEntity' as const,
      performanceEntity: {
        contentTypeAlias: 'eventEntity' as const,
        id: performanceEntity!._id,
        name: performanceEntity!.entityName,
        type: performanceEntity?.entityTypeSlug,
        relevantURLs:
          performanceEntity?.entityRelevantURLs &&
          performanceEntity?.entityRelevantURLs.length > 0
            ? performanceEntity.entityRelevantURLs.map((url?: UmbracoLink) =>
                mapUmbracoLink(url)
              )
            : [],
        musicbrainzId: performanceEntity?.entityMusicbrainzID,
        url: mapUmbracoLink(performanceEntity?.entityURL),
      },
      performanceEntityRole: entity.content.performanceEntityRole,
    } as ProductionMetadata['entities'][number])
  })

  // Notify about unpublished entities
  if (unpublishedEntitiesRoles.length > 0) {
    const notificationTitle = `[parseMetadata] Entities not published, hence they won't be used in the feeds.`
    const venueEventWithUnpublishedEntities = {
      event: `${productionContent.name} (${productionContent.showSlug})`,
      venue: `${productionContent.venueConfig?.name} (${productionContent.venueConfig?.venueSlug})`,
      unpublishedEntitiesRoles: unpublishedEntitiesRoles.join(', '),
    }

    logger.warn(venueEventWithUnpublishedEntities, notificationTitle)

    // fire-and-forget: we don’t care about the webhook’s result
    void notifyGenericWarningSlack(
      notificationTitle,
      JSON.stringify(venueEventWithUnpublishedEntities, null, 2)
    ).catch((err) => {
      // surface failures without crashing the process
      console.error('Slack notification failed:', err)
    })
  }
  return publishedEntities
}

const parseMetadata = (
  p: ProductionContent,
  tz: string | undefined,
  logger: Logger
): ProductionMetadata => {
  const publishedEntities = getPublishedEntities(p, logger)

  const base = buildEditorialMeta(p, tz)
  const social = buildSocialMetaData(
    (p.socialMetaData || {}) as SocialMetaInput
  )

  return safeParseOrWarn(
    ProductionMetadataSchema,
    {
      ...base,
      socialMetaData: social,
      entities: publishedEntities,
    },
    p,
    logger
  )
}

const parseDetails = (
  p: ProductionContent,
  tz: string | undefined,
  onSale: OnSaleRangeDate[],
  logger: Logger
): EventDetails =>
  safeParseOrWarn(
    EventDetailsSchema,
    {
      genres: p.genreList,
      title: p.eventName.trim(),
      description: p.eventDescription,
      subTitle: p.eventSubName?.trim(),
      groupInformation: p.groupInformation,
      doorsOpen: p.doorsOpen,
      startTime: p.startTime,
      runningTime: p.runningTime,
      firstPerformanceDate: normalizeDate(p.firstPerformanceDate, tz),
      performanceTimes: p.performanceTimes,
      intervals: p.intervals,
      isSoldOut: p.isSoldOut,
      salesPeriod: p.salesPeriod,
      onSaleDates: onSale,
      embargoDate: normalizeDate(p.embargoDate, tz),
      externalURL: p.externalURL,
      pricingDescription: p.pricingDescription,
      accessPerformances: umbracoAccessibilityMapper(p.accessPerformances),
      resellAvailable: p.resellAvailable,
      resellURL: p.resellURL,
      resellInformation: p.resellInformation,
    },
    p,
    logger
  )

const parseMedia = (p: ProductionContent, logger: Logger): ProductionMedia =>
  safeParseOrWarn(
    ProductionMediaSchema,
    {
      showCardImage: mapUmbracoImage(p.showCardImage),
      productionShot: mapUmbracoImage(p.productionShot),
      productionShotTall: mapUmbracoImage(p.productionShotTall),
      checkoutCalendarVerticalImage: mapUmbracoImage(
        p.checkoutCalendarVerticalImage
      ),
      checkoutCalendarHorizontalImage: mapUmbracoImage(
        p.checkoutCalendarHorizontalImage
      ),
    },
    p,
    logger
  )

const parseCtas = (p: ProductionContent, logger: Logger) => {
  const mapped = safeParseOrWarn(
    ProductionCtasSchema,
    {
      primary: p.externalURL
        ? { url: p.externalURL, label: p.ctaOverride }
        : undefined,
      secondary: p.additionalCTAURL
        ? { url: p.additionalCTAURL.url, label: p.additionalCTA }
        : undefined,
    },
    p,
    logger
  )
  return mapped.primary || mapped.secondary ? mapped : undefined
}

const parseSettings = (p: ProductionContent, logger: Logger) =>
  safeParseOrWarn(
    ProductionSettingsSchema,
    {
      informationBars: p.informationBars
        .filter((b) => b.informationBar)
        .map((b) => b.informationBar),
      brandConfig: { availableLanguages: Object.keys(p._urls) },
      overrides: {
        showPageUrl: p.showPageUrlOverride,
        queueId: p.useSpecificQueue ? p.overrideQueueID : undefined,
        upsells: p.overrideUpsells,
        buyTicketsUrl: p.buyTicketsUrlOverride?.url.trim(),
      },
      hide: {
        breadcrumbs: p.hideFromBreadcrumbs,
        infoBars: p.hideInfoBars,
        searchEngines: p.hideFromSearchEngines,
      },
      noIndex: p.noIndex,
      showPageLabel: p.showPageLabel,
      forceFreeEvent: p.forceFreeEvent,
      specialEventType: p.specialEventType,
      buyTicketsLabel: p.buyTicketsLabel,
      showPromoCodeBox: p.showPromoCodeBox,
      warningsAndGuidance: p.warningsAndGuidance,
      forcePostponedOrCanceled: p.forcePostponedOrCanceled,
    } as ProductionSettings,
    p,
    logger
  )

/* ═════════════════ root switch ═════════════════ */

export const mapUmbracoBody = (
  data: Record<string, unknown>,
  logger: Logger
) => {
  const parsedObject = parseUmbracoStringFields(data) as Record<string, unknown>

  try {
    switch (parsedObject.contentTypeAlias) {
      case 'production':
        return mapUmbracoProductionToCMS(
          parsedObject as WithBrand<ProductionContent>,
          logger
        )

      case 'aggregatorSite':
      case 'multiVenueSite':
      case 'singleVenueSite':
        return mapUmbracoBrandToCMS(parsedObject as BrandContent, logger)

      case 'contentPage':
        return mapUmbracoContentToCMS(parsedObject as UmbracoContent)

      case 'venueConfig':
        return mapUmbracoVenueConfigToCMS(parsedObject as UmbracoVenueConfig)

      case 'inventory':
        return mapUmbracoInventoryToCMS(parsedObject as InventoryContent)
      case 'wifiLandingVenue':
        return mapUmbracoWifiLandingContentToCMS(parsedObject as UmbracoContent)
      case 'tour':
        return mapUmbracoTourToCMS(
          parsedObject as WithBrand<TourContent>,
          logger
        )
    }
  } catch (error) {
    const err = error as ZodError
    logger.warn(
      `Error mapping ${String(parsedObject.contentTypeAlias)}: ${String(
        parsedObject._id
      )} – ${JSON.stringify({
        data: parsedObject,
        issues: err.issues,
      })}`
    )
  }

  /* fall back to raw data if no mapping is found */
  return data
}

/* ═════════════ Production mapper (top-level) ═════════════ */

const mapUmbracoProductionToCMS = (
  payload: WithBrand<ProductionContent>,
  logger: Logger
): Production => {
  const { brand, pathDomain } = getBrandAndPathDomain(payload._url)

  const venue = parseVenue(payload, logger)
  const onSaleDates = parseOnSaleDates(payload, venue.timeZoneLocation, logger)
  const metadata = parseMetadata(payload, venue.timeZoneLocation, logger)
  const details = parseDetails(
    payload,
    venue.timeZoneLocation,
    onSaleDates,
    logger
  )
  const media = parseMedia(payload, logger)
  const ctas = parseCtas(payload, logger)
  const settings = parseSettings(payload, logger)
  const blocks = mapUmbracoBlocks(payload.blocks)

  const production = safeParseOrWarn(
    ProductionSchema,
    {
      _id: payload._id,
      name: payload.name,
      showSlug: payload.showSlug.toLowerCase(),
      websiteSlug: payload.websiteSlug?.toLowerCase() ?? payload.showSlug.toLowerCase(),
      inventorySlug: payload.inventorySlug?.toLowerCase() ?? payload.showSlug.toLowerCase(),
      pageSlug: extractPageSlug(payload._url),
      brand: payload.brand ?? brand,
      brandAlias: payload.brandAlias,
      contentTypeAlias: 'production',
      locale: payload.locale,
      pathDomain,
      pageUrl: payload._url,

      details,
      venue,
      blocks,

      upsellList: payload.upsellList.map((u) => ({
        contentTypeAlias: 'upsell',
        image: mapUmbracoImage(u.image),
        price: u.price,
        title: u.title,
        tagline: u.tagline,
        type: u.upsellType,
        description: u.description,
        inventoryId: u.inventoryID,
        internalName: u.internalName,
        unitTypeLabel: u.unitTypeLabel,
        salesLocations: u.salesLocations,
      })),
      media,
      ctas,
      metadata,
      settings,
      thirdPartyEvent: Boolean(payload.thirdPartyEvent),
    } as Production,
    payload,
    logger
  )

  return production
}

/**
 * Maps Umbraco brand content to the CMS Brand schema.
 * @param payload - The brand content payload.
 * @returns The mapped brand data in the CMS schema format.
 *          It uses the parsed and validated brand, or the mapped data if validation fails, to avoid breaking the flow.
 */
/**
 * Maps Umbraco brand content to the CMS Brand schema.
 */
const mapUmbracoBrandToCMS = (payload: BrandContent, logger: Logger): Brand => {
  const { pathDomain } = getBrandAndPathDomain(payload._url)
  const meta = buildEditorialMeta(payload, payload.brandConfig.timeZone)

  const mapped = {
    _id: payload._id,
    brand: payload.brandConfig.domainName.toLowerCase(),
    contentTypeAlias: 'brand',
    locale: payload.locale as string,
    pathDomain,
    pageUrl: payload._url,
    upsellList: payload.upsellList.map((upsell) => ({
      contentTypeAlias: 'upsell',
      image: mapUmbracoImage(upsell.image),
      price: upsell.price,
      title: upsell.title,
      tagline: upsell.tagline,
      type: upsell.upsellType,
      description: upsell.description,
      inventoryId: upsell.inventoryID,
      internalName: upsell.internalName,
      unitTypeLabel: upsell.unitTypeLabel,
      salesLocations: upsell.salesLocations,
    })),
    socialLinks: payload.socialLinks.map((link) => ({
      contentTypeAlias: 'socialLink',
      url: link.url,
      name: link.name,
      type: link.type,
    })),
    name: payload.name,
    brandName: payload.brandName,
    blocks: mapUmbracoBlocks(payload.blocks),
    logo: mapUmbracoImage(payload.logo),
    logoName: payload.logoName,
    metadata: {
      ...meta,
      seoVenueInfo: payload.venueSchema?.map((venue) => ({
        addressLocality: venue.addressLocality,
        contentTypeAlias: venue.contentTypeAlias,
        description: venue.venueDescription,
        address: {
          city: venue.addressLocality,
          country: venue.addressCountry,
          postalCode: venue.postalCode,
          street1: venue.streetAddress,
          geoCoordinates: {
            latitude: venue.geoCoordinatesLatitude,
            longitude: venue.geoCoordinatesLongitude,
          },
        },
        hours: {
          friday: venue.fridayHours,
          monday: venue.mondayHours,
          saturday: venue.saturdayHours,
          sunday: venue.sundayHours,
          thursday: venue.thursdayHours,
          tuesday: venue.tuesdayHours,
          wednesday: venue.wednesdayHours,
        },
        image: mapUmbracoImage(venue.venueImage),
        name: venue.venueName,
        slug: venue.venueSlug,
        postalCode: venue.postalCode,
        telephoneNumber: venue.telephoneNumber,
        url: venue.venueURL.url,
      })),
    },
    settings: {
      isSingleVenueSite: payload.contentTypeAlias === 'singleVenueSite',
      siteName: payload.brandName,
      informationBars: payload.informationBars
        .filter((bar) => bar.informationBar)
        .map((bar) => bar.informationBar),
      brandConfig: {
        pageUrl: payload.brandConfig._url,
        _id: payload.brandConfig._id,
        awsRegion: mapAwsRegion(payload.brandConfig.awsRegion),
        brandAlias: payload.brandConfig.brandAlias,
        country: payload.brandConfig.country,
        currency: payload.brandConfig.currency,
        defaultLanguage: payload.brandConfig.defaultLanguage,
        domainName: payload.brandConfig.domainName,
        gtmID: payload.brandConfig.gtmID,
        gtmIDWebview: payload.brandConfig.gtmIDWebview,
        adaScriptId: payload.brandConfig.adaScriptId,
        oneTrustId: payload.brandConfig.oneTrustId,
        inventorySystem: payload.brandConfig.inventorySystem
          ? mapUmbracoInventoryToCMS(payload.brandConfig.inventorySystem)
          : undefined,
        mondayWeekStart: payload.brandConfig.mondayWeekStart ?? true,
        previewSecretPrivate: payload.brandConfig.previewSecretPrivate,
        themeKey: payload.brandConfig.themeKey,
        contentTypeAlias: 'brandConfig',
        name: payload.brandConfig.name,
        timeZone: payload.brandConfig.timeZone,
        venues: payload.brandConfig.venues.map((venue) => ({
          name: venue.venueName,
          slug: venue.venueSlug.toLowerCase(),
        })),
        availableLanguages:
          payload._urls instanceof Object ? Object.keys(payload._urls) : [],
      },
      footerNavigation: payload.footerNavigationGroup.map(
        ({ link, secondaryLinks }) => {
          const navLinks = secondaryLinks.map((secondaryLink) => ({
            label: secondaryLink?.name,
            href: secondaryLink?.url,
          }))
          return {
            title: link?.name,
            navLinks,
          }
        }
      ),
      headerNavigation: payload.headerNavigationGroup.map(
        ({ link, secondaryLinks }) => {
          const linkOrChildren =
            secondaryLinks.length > 0
              ? {
                  children: secondaryLinks.map((navSubItem) => ({
                    ...getIdAndLabel(navSubItem),
                    ...getLink(navSubItem),
                  })),
                }
              : getLink(link)
          return {
            ...getIdAndLabel(link),
            ...linkOrChildren,
          }
        }
      ),
      noIndex: payload.noIndex,
      hide: {
        breadcrumbs: payload.hideFromBreadcrumbs,
        infoBars: payload.hideInfoBars,
      },
    },
    vouchers: payload.voucherDetails
      ? payload.voucherDetails.map((voucher) => ({
          contentTypeAlias: 'vouchers',
          name: voucher.voucherName,
          image: mapUmbracoImage(voucher.voucherImage),
          guid: voucher.voucherGUID,
          designType: voucher.voucherDesignType,
          upsells:
            voucher.voucherUpsell?.map((upsell) => ({
              contentTypeAlias: 'voucherUpsell',
              guid: upsell.voucherUpsellGUID,
              name: upsell.voucherUpsellName,
              image: mapUmbracoImage(upsell.voucherUpsellImage),
              description: upsell.voucherUpsellDescription,
              price: upsell.voucherUpsellPrice,
              type: upsell.voucherUpsellType,
            })) ?? [],
        }))
      : [],
  } as Brand

  // now rely on the shared helper for parsing + warning
  return safeParseOrWarn(BrandSchema, mapped, payload, logger)
}

/**
 * Maps Umbraco content to the CMS Content schema.
 * @param payload - The content payload.
 * @returns The mapped content data in the CMS schema format.
 *          It uses the parsed and validated content, or the mapped data if validation fails, to avoid breaking the flow.
 */
export const mapUmbracoContentToCMS = (payload: UmbracoContent) => {
  const { pathDomain, brand } = getBrandAndPathDomain(payload._url)

  const mapped: Content = {
    _id: payload._id,
    contentTypeAlias: payload.contentTypeAlias,
    locale: payload.locale as string,
    pageUrl: payload._url,
    pathDomain,
    name: payload.name,
    brand,
    blocks: mapUmbracoBlocks(payload.blocks as Block[]),
    metadata: buildEditorialMeta(payload, ''),
  }

  const parsed = ContentSchema.safeParse(mapped)
  return parsed.success ? parsed.data : mapped
}

/**
 * Maps Umbraco WiFi landing content to the CMS WifiContent schema.
 * @param payload - The WiFi landing content payload.
 * @returns The mapped WiFi landing content in the CMS schema format.
 *          It uses the parsed and validated wifi landing content, or the mapped data if validation fails, to avoid breaking the flow.
 */
export const mapUmbracoWifiLandingContentToCMS = (
  payload: UmbracoContent
): WifiContent => {
  const { pathDomain, brand } = getBrandAndPathDomain(payload._url)

  const mapped: WifiContent = {
    _id: payload._id,
    contentTypeAlias: payload.contentTypeAlias,
    locale: payload.locale as string,
    pageUrl: payload._url,
    pathDomain,
    name: payload.name,
    brand,
    logoName: payload.wifiLogoName as string,
    venueName: payload.wifiVenueName as string,
    bannerImage: mapUmbracoImage(payload.wifiBannerImage as UmbracoImage),
    socialLinks: payload.wifiSocialLinks as Link[],
    loginLogoName: payload.wifiLoginLogoName as string,
    provider: payload.wifiProvider as string,
    customWelcomeText: payload.wifiCustomWelcomeText as string,
    blocks: mapUmbracoBlocks(payload.wifiBlocks as Block[]),
    metadata: buildEditorialMeta(payload, ''),
  }

  const parsed = WifiContentSchema.safeParse(mapped)
  return parsed.success ? parsed.data : mapped
}

/**
 * Stand-alone VenueConfig documents (contentTypeAlias === "venueConfig")
 * are mapped to the **same** unified Venue schema so downstream code
 * always receives a complete object.
 */
const mapUmbracoVenueConfigToCMS = (payload: UmbracoVenueConfig): Venue => ({
  id: payload._id,
  contentTypeAlias: 'venue',
  name: payload.venueConfigName,
  slug: payload.venueSlug.toLowerCase(),
  isATGVenue: true,
  upsellTypes: [],
  config: {
    configName: payload.venueConfigName,
    venueConfigSlug: payload.venueSlug.toLowerCase(),
    inventoryId: payload.inventoryID,
    hideMembershipPromotion: false,
    isThirdParty: false,
  },
})

/**
 * Maps Umbraco inventory content to the CMS Inventory schema.
 * @param payload - The inventory content payload.
 * @returns The mapped inventory content in the CMS schema format.
 */
const mapUmbracoInventoryToCMS = (payload: InventoryContent): Inventory => ({
  slug: payload.slug,
  awsRegion: mapAwsRegion(payload.awsRegion),
  type: payload.inventoryType as 'Audience View',
  timeZoneLocation: payload.timeZoneLocation,
  dataFieldMappings: payload.dataFieldMappings.map((m) => ({
    sourceField: m.content.sourceField,
    destinationField: m.content.destinationField,
  })),
  _id: payload._id,
  contentTypeAlias: 'inventory',
  name: payload.name,
  metadata: buildAuditMeta(payload),
})

/**
 * Maps Umbraco tour content to validated CMS Tour schema.
 * @param payload - The tour content payload.
 * @param logger - Logger instance for warnings and debugging.
 * @returns The validated CMS tour data.
 */

const mapUmbracoTourToCMS = (
  payload: WithBrand<TourContent>,
  logger: Logger
): Tour => {
  const { brand, pathDomain } = getBrandAndPathDomain(payload._url)

  const mapped: Tour = {
    _id: payload._id,
    name: payload.name,
    contentTypeAlias: 'tour' as const,
    locale: payload.locale as string,
    pageUrl: payload._url,
    pathDomain,
    brand: payload.brand ?? brand,
    websiteSlug: payload.websiteSlug.toLowerCase(),
    brandAlias: payload.brandAlias,
    blocks: mapUmbracoBlocks(payload.blocks),
    productions: payload.productions.map((production) => ({
      showSlug: production.showSlug,
      venueSlug: production.venueSlug!,
      brandAlias: payload.brandAlias,
      id: production._id,
    })),
    details: {
      genres: payload.genreList,
      title: payload.eventName.trim(),
      salesPeriod: payload.salesPeriod,
      intervals: payload.intervals,
      runningTime: payload.runningTime,
    },
    media: {
      tourCardImage: payload.tourCardImage
        ? mapUmbracoImage(payload.tourCardImage)
        : undefined,
      tourShot: payload.aggregatorHeroImage
        ? mapUmbracoImage(payload.aggregatorHeroImage)
        : undefined,
      trailerUrl: payload.videoEmbed || undefined,
    },
    metadata: buildEditorialMeta(payload, undefined),
    isTour: true,
    isSeason: payload.isSeason,
  }
  return safeParseOrWarn(TourSchema, mapped, payload, logger)
}
